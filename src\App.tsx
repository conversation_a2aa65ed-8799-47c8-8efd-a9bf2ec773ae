import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Header from './components/Header';
import HomePage from './pages/HomePage';
import FeaturesPage from './pages/FeaturesPage';
import DemoPage from './pages/DemoPage';
import LoginPage from './pages/LoginPage';
import AuthGuard from './components/AuthGuard';
import DashboardLayout from './components/dashboard/DashboardLayout';
import DashboardHome from './pages/dashboard/DashboardHome';
import ClientsPage from './pages/dashboard/ClientsPage';

function App() {
  return (
    <>
      <Router>
        <div className="min-h-screen bg-white">
          <Routes>
            {/* Public routes */}
            <Route path="/" element={
              <>
                <Header />
                <HomePage />
              </>
            } />
            <Route path="/fonctionnalites" element={
              <>
                <Header />
                <FeaturesPage />
              </>
            } />
            <Route path="/demo" element={
              <>
                <Header />
                <DemoPage />
              </>
            } />
            <Route path="/connexion" element={<LoginPage />} />
            
            {/* Protected dashboard routes */}
            <Route path="/dashboard" element={
              <AuthGuard>
                <DashboardLayout />
              </AuthGuard>
            }>
              <Route index element={<DashboardHome />} />
              <Route path="clients" element={
                <AuthGuard requiredRole="lawyer">
                  <ClientsPage />
                </AuthGuard>
              } />
              <Route path="cases" element={<div>Cases Page</div>} />
              <Route path="calendar" element={<div>Calendar Page</div>} />
              <Route path="messages" element={<div>Messages Page</div>} />
              <Route path="settings" element={<div>Settings Page</div>} />
            </Route>
            
            {/* Unauthorized page */}
            <Route path="/unauthorized" element={
              <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">Accès non autorisé</h1>
                  <p className="text-gray-600">Vous n'avez pas les permissions nécessaires pour accéder à cette page.</p>
                </div>
              </div>
            } />
          </Routes>
        </div>
      </Router>
      <Toaster position="top-right" />
    </>
  );
}

export default App;