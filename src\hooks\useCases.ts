import { useState, useEffect } from 'react';
import { supabase, Case } from '../lib/supabase';
import { useAuth } from './useAuth';

export const useCases = () => {
  const [cases, setCases] = useState<Case[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, profile } = useAuth();

  useEffect(() => {
    if (user && profile) {
      fetchCases();
    }
  }, [user, profile]);

  const fetchCases = async () => {
    try {
      setLoading(true);
      let query = supabase
        .from('cases')
        .select(`
          *,
          client:clients(*)
        `)
        .order('created_at', { ascending: false });

      if (profile?.role === 'lawyer') {
        // Get lawyer's cases
        const { data: lawyer } = await supabase
          .from('lawyers')
          .select('id')
          .eq('user_id', user!.id)
          .single();

        if (lawyer) {
          query = query.eq('lawyer_id', lawyer.id);
        }
      } else if (profile?.role === 'client') {
        // Get client's cases
        const { data: client } = await supabase
          .from('clients')
          .select('id')
          .eq('user_id', user!.id)
          .single();

        if (client) {
          query = query.eq('client_id', client.id);
        }
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching cases:', error);
      } else {
        setCases(data || []);
      }
    } catch (error) {
      console.error('Error fetching cases:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCase = async (caseData: Partial<Case>) => {
    try {
      // Get lawyer ID
      const { data: lawyer } = await supabase
        .from('lawyers')
        .select('id')
        .eq('user_id', user!.id)
        .single();

      if (!lawyer) {
        throw new Error('Lawyer profile not found');
      }

      // Generate case number
      const caseNumber = `CASE-${Date.now()}`;

      const { data, error } = await supabase
        .from('cases')
        .insert({
          ...caseData,
          lawyer_id: lawyer.id,
          case_number: caseNumber,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('create', 'case', data.id, { case_title: data.title });

      await fetchCases();
      return { data, error: null };
    } catch (error) {
      console.error('Error creating case:', error);
      return { data: null, error };
    }
  };

  const updateCase = async (caseId: string, updates: Partial<Case>) => {
    try {
      const { data, error } = await supabase
        .from('cases')
        .update(updates)
        .eq('id', caseId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('update', 'case', caseId, updates);

      await fetchCases();
      return { data, error: null };
    } catch (error) {
      console.error('Error updating case:', error);
      return { data: null, error };
    }
  };

  const deleteCase = async (caseId: string) => {
    try {
      const { error } = await supabase
        .from('cases')
        .delete()
        .eq('id', caseId);

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('delete', 'case', caseId);

      await fetchCases();
      return { error: null };
    } catch (error) {
      console.error('Error deleting case:', error);
      return { error };
    }
  };

  const logActivity = async (action: string, entityType: string, entityId: string, details?: any) => {
    try {
      await supabase.from('activity_logs').insert({
        user_id: user!.id,
        action,
        entity_type: entityType,
        entity_id: entityId,
        details,
      });
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  };

  return {
    cases,
    loading,
    createCase,
    updateCase,
    deleteCase,
    refreshCases: fetchCases,
  };
};