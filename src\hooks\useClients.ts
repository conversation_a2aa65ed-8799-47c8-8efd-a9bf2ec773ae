import { useState, useEffect } from 'react';
import { supabase, Client } from '../lib/supabase';
import { useAuth } from './useAuth';

export const useClients = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, profile } = useAuth();

  useEffect(() => {
    if (user && profile?.role === 'lawyer') {
      fetchClients();
    }
  }, [user, profile]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      
      // Get lawyer ID
      const { data: lawyer } = await supabase
        .from('lawyers')
        .select('id')
        .eq('user_id', user!.id)
        .single();

      if (!lawyer) {
        throw new Error('Lawyer profile not found');
      }

      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('lawyer_id', lawyer.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching clients:', error);
      } else {
        setClients(data || []);
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
    } finally {
      setLoading(false);
    }
  };

  const createClient = async (clientData: Partial<Client>) => {
    try {
      // Get lawyer ID
      const { data: lawyer } = await supabase
        .from('lawyers')
        .select('id')
        .eq('user_id', user!.id)
        .single();

      if (!lawyer) {
        throw new Error('Lawyer profile not found');
      }

      const { data, error } = await supabase
        .from('clients')
        .insert({
          ...clientData,
          lawyer_id: lawyer.id,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('create', 'client', data.id, { 
        client_name: `${data.first_name} ${data.last_name}` 
      });

      await fetchClients();
      return { data, error: null };
    } catch (error) {
      console.error('Error creating client:', error);
      return { data: null, error };
    }
  };

  const updateClient = async (clientId: string, updates: Partial<Client>) => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .update(updates)
        .eq('id', clientId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('update', 'client', clientId, updates);

      await fetchClients();
      return { data, error: null };
    } catch (error) {
      console.error('Error updating client:', error);
      return { data: null, error };
    }
  };

  const deleteClient = async (clientId: string) => {
    try {
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', clientId);

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('delete', 'client', clientId);

      await fetchClients();
      return { error: null };
    } catch (error) {
      console.error('Error deleting client:', error);
      return { error };
    }
  };

  const logActivity = async (action: string, entityType: string, entityId: string, details?: any) => {
    try {
      await supabase.from('activity_logs').insert({
        user_id: user!.id,
        action,
        entity_type: entityType,
        entity_id: entityId,
        details,
      });
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  };

  return {
    clients,
    loading,
    createClient,
    updateClient,
    deleteClient,
    refreshClients: fetchClients,
  };
};