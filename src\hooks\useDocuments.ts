import { useState, useEffect } from 'react';
import { supabase, Document } from '../lib/supabase';
import { useAuth } from './useAuth';

export const useDocuments = (caseId?: string) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    if (caseId) {
      fetchDocuments();
    }
  }, [caseId]);

  const fetchDocuments = async () => {
    if (!caseId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('documents')
        .select('*')
        .eq('case_id', caseId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching documents:', error);
      } else {
        setDocuments(data || []);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const uploadDocument = async (file: File, documentData: Partial<Document>) => {
    try {
      if (!caseId || !user) {
        throw new Error('Case ID and user required');
      }

      // Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `documents/${caseId}/${fileName}`;

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('documents')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('documents')
        .getPublicUrl(filePath);

      // Save document metadata
      const { data, error } = await supabase
        .from('documents')
        .insert({
          case_id: caseId,
          uploaded_by: user.id,
          file_name: file.name,
          file_path: publicUrl,
          file_size: file.size,
          file_type: file.type,
          ...documentData,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('upload', 'document', data.id, { 
        file_name: file.name,
        file_size: file.size 
      });

      await fetchDocuments();
      return { data, error: null };
    } catch (error) {
      console.error('Error uploading document:', error);
      return { data: null, error };
    }
  };

  const deleteDocument = async (documentId: string, filePath: string) => {
    try {
      // Delete from storage
      const path = filePath.split('/').slice(-3).join('/'); // Extract relative path
      await supabase.storage.from('documents').remove([path]);

      // Delete from database
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentId);

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('delete', 'document', documentId);

      await fetchDocuments();
      return { error: null };
    } catch (error) {
      console.error('Error deleting document:', error);
      return { error };
    }
  };

  const logActivity = async (action: string, entityType: string, entityId: string, details?: any) => {
    try {
      await supabase.from('activity_logs').insert({
        user_id: user!.id,
        action,
        entity_type: entityType,
        entity_id: entityId,
        details,
      });
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  };

  return {
    documents,
    loading,
    uploadDocument,
    deleteDocument,
    refreshDocuments: fetchDocuments,
  };
};