import { useState, useEffect } from 'react';
import { supabase, Message } from '../lib/supabase';
import { useAuth } from './useAuth';

export const useMessages = (caseId?: string) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, profile } = useAuth();

  useEffect(() => {
    if (caseId) {
      fetchMessages();
      subscribeToMessages();
    }

    return () => {
      // Cleanup subscription
    };
  }, [caseId]);

  const fetchMessages = async () => {
    if (!caseId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('case_id', caseId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching messages:', error);
      } else {
        setMessages(data || []);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const subscribeToMessages = () => {
    if (!caseId) return;

    const subscription = supabase
      .channel(`messages:${caseId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `case_id=eq.${caseId}`,
        },
        (payload) => {
          setMessages((prev) => [...prev, payload.new as Message]);
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  };

  const sendMessage = async (content: string, messageType: 'text' | 'file' = 'text', fileUrl?: string) => {
    try {
      if (!caseId || !user || !profile) {
        throw new Error('Case ID, user, and profile required');
      }

      const { data, error } = await supabase
        .from('messages')
        .insert({
          case_id: caseId,
          sender_id: user.id,
          sender_type: profile.role as 'lawyer' | 'client',
          content,
          message_type: messageType,
          file_url: fileUrl,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Log activity
      await logActivity('send', 'message', data.id, { 
        message_type: messageType,
        content_length: content.length 
      });

      return { data, error: null };
    } catch (error) {
      console.error('Error sending message:', error);
      return { data: null, error };
    }
  };

  const markAsRead = async (messageId: string) => {
    try {
      const { error } = await supabase
        .from('messages')
        .update({ is_read: true })
        .eq('id', messageId);

      if (error) {
        throw error;
      }

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, is_read: true } : msg
        )
      );

      return { error: null };
    } catch (error) {
      console.error('Error marking message as read:', error);
      return { error };
    }
  };

  const logActivity = async (action: string, entityType: string, entityId: string, details?: any) => {
    try {
      await supabase.from('activity_logs').insert({
        user_id: user!.id,
        action,
        entity_type: entityType,
        entity_id: entityId,
        details,
      });
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  };

  return {
    messages,
    loading,
    sendMessage,
    markAsRead,
    refreshMessages: fetchMessages,
  };
};