import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Lawyer {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  bar_number?: string;
  specialization?: string[];
  office_address?: string;
  profile_image_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Client {
  id: string;
  user_id?: string;
  lawyer_id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  cin?: string;
  address?: string;
  profession?: string;
  birth_date?: string;
  profile_image_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Case {
  id: string;
  lawyer_id: string;
  client_id: string;
  case_number: string;
  title: string;
  description?: string;
  case_type: string;
  status: 'active' | 'pending' | 'closed' | 'archived';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  court_name?: string;
  opposing_party?: string;
  opposing_lawyer?: string;
  start_date?: string;
  end_date?: string;
  estimated_value?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  client?: Client;
}

export interface Document {
  id: string;
  case_id: string;
  uploaded_by: string;
  file_name: string;
  file_path: string;
  file_size?: number;
  file_type?: string;
  document_type?: string;
  description?: string;
  ocr_text?: string;
  is_signed: boolean;
  signature_data?: any;
  created_at: string;
}

export interface Message {
  id: string;
  case_id: string;
  sender_id: string;
  sender_type: 'lawyer' | 'client';
  content: string;
  message_type: 'text' | 'file' | 'system';
  file_url?: string;
  is_read: boolean;
  created_at: string;
}

export interface Appointment {
  id: string;
  lawyer_id: string;
  client_id: string;
  case_id: string;
  title: string;
  description?: string;
  appointment_type: 'meeting' | 'hearing' | 'consultation' | 'court';
  start_time: string;
  end_time: string;
  location?: string;
  status: 'scheduled' | 'confirmed' | 'cancelled' | 'completed';
  reminder_sent: boolean;
  created_at: string;
  updated_at: string;
  client?: Client;
  case?: Case;
}

export interface ActivityLog {
  id: string;
  user_id: string;
  action: string;
  entity_type: string;
  entity_id?: string;
  details?: any;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  entity_type?: string;
  entity_id?: string;
  is_read: boolean;
  created_at: string;
}

export interface UserProfile {
  id: string;
  user_id: string;
  role: 'lawyer' | 'client' | 'admin';
  profile_id?: string;
  created_at: string;
}