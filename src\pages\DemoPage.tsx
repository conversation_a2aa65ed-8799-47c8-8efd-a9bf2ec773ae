import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Monitor, 
  Smartphone, 
  Users, 
  FileText, 
  Calendar, 
  BarChart, 
  Plus,
  Search,
  Bell,
  Settings,
  User
} from 'lucide-react';

const DemoPage = () => {
  const [activeView, setActiveView] = useState<'desktop' | 'mobile'>('desktop');
  const [activeTab, setActiveTab] = useState('dashboard');

  const demoTabs = [
    { id: 'dashboard', name: 'Dashboard', icon: Bar<PERSON><PERSON> },
    { id: 'clients', name: 'Clients', icon: Users },
    { id: 'dossiers', name: 'Dossie<PERSON>', icon: FileText },
    { id: 'agenda', name: 'Agenda', icon: Calendar }
  ];

  const mockData = {
    dashboard: {
      stats: [
        { label: 'Clients actifs', value: '127', change: '+12%' },
        { label: 'Dossiers en cours', value: '43', change: '+5%' },
        { label: 'Audiences ce mois', value: '18', change: '+8%' },
        { label: 'Revenus gén<PERSON>', value: '45,200 TND', change: '+15%' }
      ],
      recentActivity: [
        { type: 'client', name: 'Ahmed Ben Salem', action: 'Nouveau client ajouté', time: 'Il y a 2h' },
        { type: 'dossier', name: 'Affaire succession', action: 'Document ajouté', time: 'Il y a 4h' },
        { type: 'audience', name: 'Tribunal Tunis', action: 'Audience planifiée', time: 'Il y a 1j' }
      ]
    },
    clients: [
      { name: 'Ahmed Ben Salem', type: 'Particulier', dossiers: 3, status: 'Actif' },
      { name: 'SARL TechCorp', type: 'Entreprise', dossiers: 7, status: 'Actif' },
      { name: 'Fatma Trabelsi', type: 'Particulier', dossiers: 2, status: 'En attente' }
    ],
    dossiers: [
      { title: 'Divorce contentieux', client: 'Ahmed Ben Salem', status: 'En cours', tribunal: 'Tunis' },
      { title: 'Création société', client: 'SARL TechCorp', status: 'Terminé', tribunal: 'Ariana' },
      { title: 'Succession', client: 'Fatma Trabelsi', status: 'En attente', tribunal: 'Sousse' }
    ]
  };

  const DesktopDemo = () => (
    <div className="bg-gray-900 rounded-lg overflow-hidden shadow-2xl">
      {/* Window Header */}
      <div className="bg-gray-800 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        </div>
        <div className="text-gray-300 text-sm">Nechi & Sniha - Dashboard</div>
        <div className="w-16"></div>
      </div>

      {/* App Content */}
      <div className="bg-white h-96 flex">
        {/* Sidebar */}
        <div className="w-64 bg-gray-50 border-r border-gray-200 p-4">
          <div className="space-y-2">
            {demoTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === tab.id 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          <AnimatePresence mode="wait">
            {activeTab === 'dashboard' && (
              <motion.div
                key="dashboard"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-6"
              >
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                    Nouveau dossier
                  </button>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  {mockData.dashboard.stats.map((stat, index) => (
                    <div key={index} className="bg-white border rounded-lg p-4">
                      <div className="text-sm text-gray-600">{stat.label}</div>
                      <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                      <div className="text-sm text-green-600">{stat.change}</div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'clients' && (
              <motion.div
                key="clients"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-gray-900">Clients</h2>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                    Ajouter client
                  </button>
                </div>
                
                <div className="space-y-3">
                  {mockData.clients.map((client, index) => (
                    <div key={index} className="bg-white border rounded-lg p-4 flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-gray-900">{client.name}</div>
                        <div className="text-sm text-gray-600">{client.type} • {client.dossiers} dossiers</div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        client.status === 'Actif' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {client.status}
                      </span>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );

  const MobileDemo = () => (
    <div className="w-80 mx-auto">
      <div className="bg-gray-900 rounded-2xl p-2 shadow-2xl">
        <div className="bg-white rounded-xl overflow-hidden">
          {/* Mobile Header */}
          <div className="bg-blue-600 text-white p-4 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <User className="h-6 w-6" />
              <div>
                <div className="font-semibold">Me Alaa Nechi</div>
                <div className="text-xs text-blue-200">Cabinet Nechi & Sniha</div>
              </div>
            </div>
            <Bell className="h-6 w-6" />
          </div>

          {/* Mobile Content */}
          <div className="h-96 overflow-y-auto">
            <div className="p-4 space-y-4">
              {/* Quick Actions */}
              <div className="grid grid-cols-2 gap-3">
                <button className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-left">
                  <Plus className="h-6 w-6 text-blue-600 mb-2" />
                  <div className="text-sm font-medium text-gray-900">Nouveau dossier</div>
                </button>
                <button className="bg-green-50 border border-green-200 rounded-lg p-3 text-left">
                  <Users className="h-6 w-6 text-green-600 mb-2" />
                  <div className="text-sm font-medium text-gray-900">Ajouter client</div>
                </button>
              </div>

              {/* Recent Activity */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Activité récente</h3>
                <div className="space-y-3">
                  {mockData.dashboard.recentActivity.map((activity, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-3">
                      <div className="font-medium text-gray-900 text-sm">{activity.name}</div>
                      <div className="text-xs text-gray-600">{activity.action}</div>
                      <div className="text-xs text-gray-500 mt-1">{activity.time}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="border-t border-gray-200 p-2">
            <div className="flex justify-around">
              {demoTabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`p-2 rounded-lg ${
                    activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'text-gray-600'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Démonstration Interactive
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Explorez l'interface de Nechi & Sniha sur desktop et mobile. 
            Découvrez la fluidité et l'ergonomie de notre solution.
          </p>

          {/* View Toggle */}
          <div className="inline-flex bg-gray-200 rounded-lg p-1">
            <button
              onClick={() => setActiveView('desktop')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
                activeView === 'desktop' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Monitor className="h-4 w-4" />
              <span>Desktop</span>
            </button>
            <button
              onClick={() => setActiveView('mobile')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
                activeView === 'mobile' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Smartphone className="h-4 w-4" />
              <span>Mobile</span>
            </button>
          </div>
        </motion.div>

        {/* Demo Container */}
        <motion.div
          key={activeView}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="flex justify-center"
        >
          {activeView === 'desktop' ? <DesktopDemo /> : <MobileDemo />}
        </motion.div>

        {/* Features Highlight */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="bg-white rounded-xl p-8 shadow-lg max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Interface conçue pour les avocats
            </h2>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Monitor className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Interface Intuitive</h3>
                <p className="text-sm text-gray-600">
                  Navigation simple et ergonomique adaptée au workflow juridique
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Smartphone className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Mobile-First</h3>
                <p className="text-sm text-gray-600">
                  Application native optimisée pour le travail en mobilité
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <BarChart className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Tableaux de Bord</h3>
                <p className="text-sm text-gray-600">
                  Visualisation claire de vos KPIs et métriques importantes
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mt-12 text-center"
        >
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Prêt à tester la solution complète ?
            </h3>
            <p className="text-blue-100 mb-6">
              Demandez votre accès démo personnalisé avec vos propres données
            </p>
            <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
              Demander un accès démo
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DemoPage;