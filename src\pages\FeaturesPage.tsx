import React from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  FileText, 
  Calendar, 
  Archive, 
  Search, 
  Download, 
  Smartphone, 
  Bell, 
  BarChart, 
  Mic, 
  Brain, 
  Shield,
  Clock,
  Globe,
  Zap
} from 'lucide-react';

const FeaturesPage = () => {
  const mainFeatures = [
    {
      icon: Users,
      title: 'Gestion des Clients',
      description: 'Créez des fiches clients complètes avec historique détaillé, documents associés et suivi personnalisé.',
      details: [
        'Fiches clients complètes avec photos',
        'Historique des interactions et rendez-vous',
        'Gestion des contacts multiples par client',
        'Catégorisation et étiquetage avancé'
      ]
    },
    {
      icon: FileText,
      title: 'Dossiers Juridiques',
      description: 'Organisez vos affaires par type, tribunal, statut avec un système de classification intelligent.',
      details: [
        'Classification par domaine juridique',
        'Suivi du statut en temps réel',
        'Liaison automatique aux clients',
        'Templates de documents pré-configurés'
      ]
    },
    {
      icon: Calendar,
      title: 'Agenda Intelligent',
      description: 'Planifiez audiences, rendez-vous et rappels avec synchronisation automatique sur tous vos appareils.',
      details: [
        'Calendrier interactif avec vues multiples',
        'Rappels automatiques configurables',
        'Synchronisation Google/Outlook',
        'Gestion des conflits d\'horaires'
      ]
    },
    {
      icon: Archive,
      title: 'Archivage Numérique',
      description: 'Stockez et organisez tous vos documents (PDF, Word, images) avec recherche OCR intégrée.',
      details: [
        'Stockage cloud sécurisé illimité',
        'OCR intelligent pour tous documents',
        'Versioning automatique des fichiers',
        'Partage sécurisé avec clients'
      ]
    },
    {
      icon: Search,
      title: 'Moteur de Recherche',
      description: 'Trouvez instantanément clients, dossiers ou documents grâce à l\'IA de recherche avancée.',
      details: [
        'Recherche sémantique intelligente',
        'Filtres avancés multicritères',
        'Suggestions automatiques',
        'Recherche dans le contenu des documents'
      ]
    },
    {
      icon: Download,
      title: 'Export Automatique',
      description: 'Générez rapports, factures et documents officiels en PDF avec mise en forme professionnelle.',
      details: [
        'Templates personnalisables',
        'Export en lot automatisé',
        'Signature électronique intégrée',
        'Envoi automatique par email'
      ]
    }
  ];

  const advancedFeatures = [
    {
      icon: Smartphone,
      title: 'Application Mobile',
      description: 'Application Flutter native pour iOS et Android avec synchronisation temps réel.',
      color: 'bg-green-500'
    },
    {
      icon: Bell,
      title: 'Notifications Push',
      description: 'Alertes intelligentes via Firebase pour ne rater aucune échéance importante.',
      color: 'bg-yellow-500'
    },
    {
      icon: BarChart,
      title: 'Business Intelligence',
      description: 'Tableaux de bord avec KPIs, statistiques et analyses de performance.',
      color: 'bg-purple-500'
    },
    {
      icon: Mic,
      title: 'Assistant Vocal',
      description: 'Commandes vocales pour créer événements, rechercher dossiers ou ajouter notes.',
      color: 'bg-blue-500'
    },
    {
      icon: Brain,
      title: 'IA Prédictive',
      description: 'Analyse de probabilité de succès basée sur l\'historique et la jurisprudence.',
      color: 'bg-indigo-500'
    },
    {
      icon: Shield,
      title: 'Sécurité Renforcée',
      description: 'Chiffrement AES 256, authentification biométrique et conformité RGPD.',
      color: 'bg-red-500'
    }
  ];

  const techStack = [
    { name: 'React.js & TypeScript', icon: Globe, description: 'Frontend moderne et typé' },
    { name: 'Laravel API', icon: Zap, description: 'Backend robuste et sécurisé' },
    { name: 'PostgreSQL', icon: Archive, description: 'Base de données performante' },
    { name: 'Firebase FCM', icon: Bell, description: 'Notifications en temps réel' },
    { name: 'OCR & IA', icon: Brain, description: 'Intelligence artificielle intégrée' },
    { name: 'Flutter Mobile', icon: Smartphone, description: 'Apps natives iOS/Android' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <section className="bg-gradient-to-br from-blue-900 to-gray-900 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl lg:text-5xl font-bold">
              Fonctionnalités Complètes
            </h1>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Découvrez toutes les fonctionnalités qui font de Nechi & Sniha 
              la solution de gestion juridique la plus avancée de Tunisie.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Main Features */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12">
            {mainFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                className="bg-white border border-gray-200 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                    <p className="text-gray-600 mb-4">{feature.description}</p>
                    <ul className="space-y-2">
                      {feature.details.map((detail, idx) => (
                        <li key={idx} className="flex items-center text-sm text-gray-600">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3"></div>
                          {detail}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Advanced Features */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Technologies Avancées
            </h2>
            <p className="text-xl text-gray-600">
              Fonctionnalités de pointe pour une expérience utilisateur exceptionnelle
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {advancedFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                whileHover={{ y: -5 }}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className={`w-12 h-12 ${feature.color} rounded-lg flex items-center justify-center mb-4`}>
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Tech Stack */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Stack Technologique
            </h2>
            <p className="text-xl text-gray-600">
              Technologies modernes pour une performance et sécurité optimales
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {techStack.map((tech, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                className="bg-gray-50 rounded-lg p-6 text-center hover:bg-blue-50 transition-colors duration-300"
              >
                <tech.icon className="h-10 w-10 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-1">{tech.name}</h3>
                <p className="text-sm text-gray-600">{tech.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-800 to-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <h2 className="text-3xl font-bold">
              Découvrez Nechi & Sniha en action
            </h2>
            <p className="text-lg text-blue-100">
              Testez toutes ces fonctionnalités avec notre démonstration interactive
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-all duration-300"
            >
              Voir la démonstration
            </motion.button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default FeaturesPage;