import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Scale, 
  Shield, 
  Zap, 
  Brain, 
  Smartphone, 
  ArrowRight,
  CheckCircle,
  Users,
  FileText,
  Calendar
} from 'lucide-react';

const HomePage = () => {
  const features = [
    {
      icon: Users,
      title: 'Gestion Clients',
      description: 'Fiches complètes et historique détaillé'
    },
    {
      icon: FileText,
      title: 'Dossiers Juridiques',
      description: 'Organisation intelligente de vos affaires'
    },
    {
      icon: Calendar,
      title: 'Agenda Intelligent',
      description: 'Audiences et rappels automatiques'
    },
    {
      icon: Brain,
      title: 'IA Intégrée',
      description: 'Assistant vocal et analyse prédictive'
    }
  ];

  const stats = [
    { number: '500+', label: 'Cabinets utilisateurs' },
    { number: '99.9%', label: 'Uptime garanti' },
    { number: '24/7', label: 'Support technique' },
    { number: '256-bit', label: 'Chiffrement AES' }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-gray-900 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-32 h-32 bg-blue-400 rounded-full opacity-10 animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-24 h-24 bg-white rounded-full opacity-5 animate-bounce"></div>
          <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-blue-300 rounded-full opacity-10 animate-pulse delay-300"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div className="space-y-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.6 }}
                  className="flex items-center space-x-2 text-blue-300"
                >
                  <Scale className="h-6 w-6" />
                  <span className="text-sm font-semibold tracking-wide uppercase">
                    Nouvelle Génération
                  </span>
                </motion.div>
                
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Gérez vos affaires juridiques avec
                  <span className="bg-gradient-to-r from-blue-300 to-white bg-clip-text text-transparent">
                    {' '}intelligence
                  </span>
                </h1>
                
                <p className="text-xl text-blue-100 leading-relaxed">
                  Rapidité et sécurité au service de votre cabinet. 
                  L'outil de gestion juridique le plus avancé de Tunisie.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/fonctionnalites">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full sm:w-auto bg-white text-blue-900 px-8 py-4 rounded-lg font-semibold 
                             hover:bg-blue-50 transition-all duration-300 flex items-center justify-center space-x-2 shadow-xl"
                  >
                    <span>Découvrir les fonctionnalités</span>
                    <ArrowRight className="h-5 w-5" />
                  </motion.button>
                </Link>
                
                <Link to="/demo">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full sm:w-auto border-2 border-white text-white px-8 py-4 rounded-lg font-semibold 
                             hover:bg-white hover:text-blue-900 transition-all duration-300"
                  >
                    Démo gratuite
                  </motion.button>
                </Link>
              </div>

              <div className="flex items-center space-x-6 pt-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="text-sm text-blue-100">Sécurisé AES 256</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="text-sm text-blue-100">Support 24/7</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                        <Scale className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold">Dashboard Avocat</h3>
                        <p className="text-sm text-blue-200">Vue d'ensemble</p>
                      </div>
                    </div>
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {features.map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 + index * 0.1, duration: 0.5 }}
                        className="bg-white/10 rounded-lg p-4 text-center"
                      >
                        <feature.icon className="h-8 w-8 text-blue-300 mx-auto mb-2" />
                        <h4 className="text-sm font-medium">{feature.title}</h4>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                className="text-center"
              >
                <div className="text-3xl lg:text-4xl font-bold text-blue-800 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Preview */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Fonctionnalités Avancées
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez comment notre solution révolutionne la gestion juridique
              avec des technologies de pointe et une IA intégrée.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {[
              {
                icon: Shield,
                title: 'Sécurité Maximale',
                description: 'Chiffrement AES 256, authentification biométrique et conformité RGPD',
                color: 'blue'
              },
              {
                icon: Brain,
                title: 'Intelligence Artificielle',
                description: 'Assistant vocal, analyse prédictive et résumé automatique des dossiers',
                color: 'purple'
              },
              {
                icon: Smartphone,
                title: 'Mobile & Cloud',
                description: 'Application Flutter native avec synchronisation en temps réel',
                color: 'green'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                whileHover={{ y: -5 }}
                className="bg-white border border-gray-200 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className={`w-16 h-16 bg-${feature.color}-100 rounded-lg flex items-center justify-center mb-6`}>
                  <feature.icon className={`h-8 w-8 text-${feature.color}-600`} />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-800 to-gray-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <h2 className="text-3xl lg:text-4xl font-bold">
              Prêt à révolutionner votre cabinet ?
            </h2>
            <p className="text-xl text-blue-100">
              Rejoignez les centaines d'avocats qui ont déjà adopté Nechi & Sniha
              pour moderniser leur pratique juridique.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/demo">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white text-blue-900 px-8 py-4 rounded-lg font-semibold 
                           hover:bg-blue-50 transition-all duration-300 flex items-center space-x-2 shadow-xl"
                >
                  <span>Essayer gratuitement</span>
                  <ArrowRight className="h-5 w-5" />
                </motion.button>
              </Link>
              <Link to="/connexion">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold 
                           hover:bg-white hover:text-blue-900 transition-all duration-300"
                >
                  Connexion avocat
                </motion.button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;