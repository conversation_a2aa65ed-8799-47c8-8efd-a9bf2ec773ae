import React from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  FileText, 
  Calendar, 
  TrendingUp, 
  Clock, 
  AlertCircle,
  CheckCircle,
  DollarSign
} from 'lucide-react';
import { useCases } from '../../hooks/useCases';
import { useClients } from '../../hooks/useClients';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const DashboardHome = () => {
  const { cases, loading: casesLoading } = useCases();
  const { clients, loading: clientsLoading } = useClients();

  const stats = [
    {
      name: 'Clients actifs',
      value: clients.filter(c => c.is_active).length,
      change: '+12%',
      changeType: 'positive',
      icon: Users,
      color: 'blue'
    },
    {
      name: 'Dossiers en cours',
      value: cases.filter(c => c.status === 'active').length,
      change: '+5%',
      changeType: 'positive',
      icon: FileText,
      color: 'green'
    },
    {
      name: 'Audiences ce mois',
      value: 18,
      change: '+8%',
      changeType: 'positive',
      icon: Calendar,
      color: 'purple'
    },
    {
      name: 'Revenus générés',
      value: '45,200 TND',
      change: '+15%',
      changeType: 'positive',
      icon: DollarSign,
      color: 'yellow'
    }
  ];

  const recentCases = cases.slice(0, 5);
  const urgentCases = cases.filter(c => c.priority === 'urgent' || c.priority === 'high').slice(0, 3);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      case 'archived': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (casesLoading || clientsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Tableau de bord
        </h1>
        <p className="text-gray-600">
          Bienvenue dans votre espace de gestion juridique
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className={`text-sm ${
                  stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change} ce mois
                </p>
              </div>
              <div className={`w-12 h-12 bg-${stat.color}-100 rounded-lg flex items-center justify-center`}>
                <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Recent Cases */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200"
        >
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Dossiers récents</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentCases.map((case_) => (
                <div key={case_.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{case_.title}</h3>
                    <p className="text-sm text-gray-600">{case_.client?.first_name} {case_.client?.last_name}</p>
                    <p className="text-xs text-gray-500">
                      {format(new Date(case_.created_at), 'dd MMM yyyy', { locale: fr })}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(case_.status)}`}>
                      {case_.status}
                    </span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(case_.priority)}`}>
                      {case_.priority}
                    </span>
                  </div>
                </div>
              ))}
              {recentCases.length === 0 && (
                <p className="text-gray-500 text-center py-8">Aucun dossier récent</p>
              )}
            </div>
          </div>
        </motion.div>

        {/* Urgent Cases */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200"
        >
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              Dossiers urgents
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {urgentCases.map((case_) => (
                <div key={case_.id} className="flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{case_.title}</h3>
                    <p className="text-sm text-gray-600">{case_.client?.first_name} {case_.client?.last_name}</p>
                    <p className="text-xs text-red-600 font-medium">
                      Priorité: {case_.priority}
                    </p>
                  </div>
                  <Clock className="h-5 w-5 text-red-500" />
                </div>
              ))}
              {urgentCases.length === 0 && (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                  <p className="text-gray-500">Aucun dossier urgent</p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center justify-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
            <Users className="h-6 w-6 text-blue-600 mr-3" />
            <span className="font-medium text-blue-700">Nouveau client</span>
          </button>
          <button className="flex items-center justify-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
            <FileText className="h-6 w-6 text-green-600 mr-3" />
            <span className="font-medium text-green-700">Nouveau dossier</span>
          </button>
          <button className="flex items-center justify-center p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
            <Calendar className="h-6 w-6 text-purple-600 mr-3" />
            <span className="font-medium text-purple-700">Planifier audience</span>
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default DashboardHome;