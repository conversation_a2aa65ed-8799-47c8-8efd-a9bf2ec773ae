/*
  # Schéma complet pour la plateforme juridique Nechi & Sniha

  1. Tables principales
    - `lawyers` - Profils des avocats
    - `clients` - Informations des clients
    - `cases` - Dossiers juridiques
    - `documents` - Documents et pièces jointes
    - `messages` - Chat avocat-client
    - `appointments` - Audiences et rendez-vous
    - `activity_logs` - Journal d'activité
    - `notifications` - Système de notifications

  2. Sécurité
    - RLS activé sur toutes les tables
    - Politiques d'accès basées sur les rôles
    - Chiffrement des données sensibles

  3. Fonctionnalités
    - Chat temps réel
    - Gestion documentaire
    - Système de notifications
    - Audit trail complet
*/

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Lawyers table
CREATE TABLE IF NOT EXISTS lawyers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  first_name text NOT NULL,
  last_name text NOT NULL,
  email text UNIQUE NOT NULL,
  phone text,
  bar_number text UNIQUE,
  specialization text[],
  office_address text,
  profile_image_url text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Clients table
CREATE TABLE IF NOT EXISTS clients (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  lawyer_id uuid REFERENCES lawyers(id) ON DELETE CASCADE,
  first_name text NOT NULL,
  last_name text NOT NULL,
  email text,
  phone text,
  cin text,
  address text,
  profession text,
  birth_date date,
  profile_image_url text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Cases table
CREATE TABLE IF NOT EXISTS cases (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  lawyer_id uuid REFERENCES lawyers(id) ON DELETE CASCADE,
  client_id uuid REFERENCES clients(id) ON DELETE CASCADE,
  case_number text UNIQUE NOT NULL,
  title text NOT NULL,
  description text,
  case_type text NOT NULL,
  status text DEFAULT 'active' CHECK (status IN ('active', 'pending', 'closed', 'archived')),
  priority text DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  court_name text,
  opposing_party text,
  opposing_lawyer text,
  start_date date DEFAULT CURRENT_DATE,
  end_date date,
  estimated_value numeric(15,2),
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  case_id uuid REFERENCES cases(id) ON DELETE CASCADE,
  uploaded_by uuid REFERENCES auth.users(id),
  file_name text NOT NULL,
  file_path text NOT NULL,
  file_size bigint,
  file_type text,
  document_type text,
  description text,
  ocr_text text,
  is_signed boolean DEFAULT false,
  signature_data jsonb,
  created_at timestamptz DEFAULT now()
);

-- Messages table for chat
CREATE TABLE IF NOT EXISTS messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  case_id uuid REFERENCES cases(id) ON DELETE CASCADE,
  sender_id uuid REFERENCES auth.users(id),
  sender_type text CHECK (sender_type IN ('lawyer', 'client')),
  content text NOT NULL,
  message_type text DEFAULT 'text' CHECK (message_type IN ('text', 'file', 'system')),
  file_url text,
  is_read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Appointments table
CREATE TABLE IF NOT EXISTS appointments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  lawyer_id uuid REFERENCES lawyers(id) ON DELETE CASCADE,
  client_id uuid REFERENCES clients(id) ON DELETE CASCADE,
  case_id uuid REFERENCES cases(id) ON DELETE CASCADE,
  title text NOT NULL,
  description text,
  appointment_type text DEFAULT 'meeting' CHECK (appointment_type IN ('meeting', 'hearing', 'consultation', 'court')),
  start_time timestamptz NOT NULL,
  end_time timestamptz NOT NULL,
  location text,
  status text DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'cancelled', 'completed')),
  reminder_sent boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Activity logs table
CREATE TABLE IF NOT EXISTS activity_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  action text NOT NULL,
  entity_type text NOT NULL,
  entity_id uuid,
  details jsonb,
  ip_address inet,
  user_agent text,
  created_at timestamptz DEFAULT now()
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  title text NOT NULL,
  message text NOT NULL,
  type text DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
  entity_type text,
  entity_id uuid,
  is_read boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- User profiles table to link auth.users with roles
CREATE TABLE IF NOT EXISTS user_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  role text NOT NULL CHECK (role IN ('lawyer', 'client', 'admin')),
  profile_id uuid, -- References either lawyers.id or clients.id
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE lawyers ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for lawyers
CREATE POLICY "Lawyers can read own profile"
  ON lawyers FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Lawyers can update own profile"
  ON lawyers FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

-- RLS Policies for clients
CREATE POLICY "Lawyers can read their clients"
  ON clients FOR SELECT
  TO authenticated
  USING (
    lawyer_id IN (
      SELECT id FROM lawyers WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Clients can read own profile"
  ON clients FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Lawyers can manage their clients"
  ON clients FOR ALL
  TO authenticated
  USING (
    lawyer_id IN (
      SELECT id FROM lawyers WHERE user_id = auth.uid()
    )
  );

-- RLS Policies for cases
CREATE POLICY "Lawyers can manage their cases"
  ON cases FOR ALL
  TO authenticated
  USING (
    lawyer_id IN (
      SELECT id FROM lawyers WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Clients can read their cases"
  ON cases FOR SELECT
  TO authenticated
  USING (
    client_id IN (
      SELECT id FROM clients WHERE user_id = auth.uid()
    )
  );

-- RLS Policies for documents
CREATE POLICY "Users can manage case documents"
  ON documents FOR ALL
  TO authenticated
  USING (
    case_id IN (
      SELECT c.id FROM cases c
      LEFT JOIN lawyers l ON c.lawyer_id = l.id
      LEFT JOIN clients cl ON c.client_id = cl.id
      WHERE l.user_id = auth.uid() OR cl.user_id = auth.uid()
    )
  );

-- RLS Policies for messages
CREATE POLICY "Users can manage case messages"
  ON messages FOR ALL
  TO authenticated
  USING (
    case_id IN (
      SELECT c.id FROM cases c
      LEFT JOIN lawyers l ON c.lawyer_id = l.id
      LEFT JOIN clients cl ON c.client_id = cl.id
      WHERE l.user_id = auth.uid() OR cl.user_id = auth.uid()
    )
  );

-- RLS Policies for appointments
CREATE POLICY "Lawyers can manage their appointments"
  ON appointments FOR ALL
  TO authenticated
  USING (
    lawyer_id IN (
      SELECT id FROM lawyers WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Clients can read their appointments"
  ON appointments FOR SELECT
  TO authenticated
  USING (
    client_id IN (
      SELECT id FROM clients WHERE user_id = auth.uid()
    )
  );

-- RLS Policies for activity logs
CREATE POLICY "Users can read own activity logs"
  ON activity_logs FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- RLS Policies for notifications
CREATE POLICY "Users can manage own notifications"
  ON notifications FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- RLS Policies for user profiles
CREATE POLICY "Users can read own profile"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can update own profile"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_lawyers_user_id ON lawyers(user_id);
CREATE INDEX IF NOT EXISTS idx_clients_lawyer_id ON clients(lawyer_id);
CREATE INDEX IF NOT EXISTS idx_clients_user_id ON clients(user_id);
CREATE INDEX IF NOT EXISTS idx_cases_lawyer_id ON cases(lawyer_id);
CREATE INDEX IF NOT EXISTS idx_cases_client_id ON cases(client_id);
CREATE INDEX IF NOT EXISTS idx_documents_case_id ON documents(case_id);
CREATE INDEX IF NOT EXISTS idx_messages_case_id ON messages(case_id);
CREATE INDEX IF NOT EXISTS idx_appointments_lawyer_id ON appointments(lawyer_id);
CREATE INDEX IF NOT EXISTS idx_appointments_start_time ON appointments(start_time);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_lawyers_updated_at BEFORE UPDATE ON lawyers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cases_updated_at BEFORE UPDATE ON cases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();